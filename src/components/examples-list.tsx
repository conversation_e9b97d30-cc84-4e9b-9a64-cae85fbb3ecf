'use client';

import { Button, Translate } from '@/components/ui';
import { getTranslationKeyOfLanguage } from '@/lib';
import { Language } from '@prisma/client';
import { ChevronDown, Loader2 } from 'lucide-react';
import React, { memo } from 'react';
import { useWordExamples } from '@/hooks/use-word-examples';

interface Example {
	id?: string;
	EN: string;
	VI: string;
}

interface Definition {
	id: string;
	examples: Example[];
}

interface ExamplesListProps {
	wordId: string;
	definition: Definition;
	sourceLanguage: Language;
	targetLanguage: Language;
	onGenerateExamples?: () => void;
	generatingExamples?: boolean;
}

function ExamplesListComponent({
	wordId,
	definition,
	sourceLanguage,
	targetLanguage,
	onGenerateExamples,
	generatingExamples = false,
}: ExamplesListProps) {
	const { getExampleState, loadMoreExamples } = useWordExamples();
	const exampleState = getExampleState(definition.id);

	// Combine initial examples with loaded examples, avoiding duplicates
	const allExamples = React.useMemo(() => {
		const seenIds = new Set<string>();
		const combined = [];
		
		// Add initial examples first
		for (const example of definition.examples) {
			if (example.id && !seenIds.has(example.id)) {
				seenIds.add(example.id);
				combined.push(example);
			} else if (!example.id) {
				// If no ID, add it anyway (shouldn't happen but safety)
				combined.push(example);
			}
		}
		
		// Add loaded examples, skipping duplicates
		for (const example of exampleState.examples) {
			if (example.id && !seenIds.has(example.id)) {
				seenIds.add(example.id);
				combined.push(example);
			} else if (!example.id) {
				// If no ID, add it anyway (shouldn't happen but safety)
				combined.push(example);
			}
		}
		
		return combined;
	}, [definition.examples, exampleState.examples]);

	const hasMoreExamples = exampleState.hasMore;
	const isFirstLoad = exampleState.examples.length === 0 && exampleState.total === 0;
	const totalExamplesShown = allExamples.length;
	const isLoadingExamples = exampleState.loading || generatingExamples;
	const canLoadMore =
		wordId && // Only show if we have a valid wordId
		(hasMoreExamples || isFirstLoad || totalExamplesShown < 20) &&
		!isLoadingExamples;

	const handleLoadMore = async () => {
		if (!wordId) return; // Safety check
		await loadMoreExamples(wordId, definition.id, definition.examples.length);
	};

	return (
		<div>
			<div className="flex items-center justify-between mb-1.5">
				<p className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
					<Translate text="words.examples" />:
					{isLoadingExamples && (
						<span className="flex items-center gap-1 text-xs text-muted-foreground/70">
							<Loader2 className="h-3 w-3 animate-spin" />
							<Translate text="words.loading" />
						</span>
					)}
				</p>
			</div>

			<div className="space-y-2">
				{allExamples.length > 0 ? (
					<>
						{allExamples.map((example, exIndex) => (
							<div
								key={example.id || `example-${exIndex}-${example.EN?.slice(0, 20)}-${example.VI?.slice(0, 20)}`}
								className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
							>
								<p className="text-xs font-medium text-muted-foreground tracking-wide">
									<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />:
								</p>
								<p className="mb-1 text-sm text-foreground/95">
									{example[targetLanguage] || (
										<span className="italic opacity-70">
											<Translate text="words.example_not_provided" />
										</span>
									)}
								</p>
								<p className="text-xs font-medium text-muted-foreground tracking-wide">
									<Translate text={getTranslationKeyOfLanguage(sourceLanguage)} />:
								</p>
								<p className="text-sm text-foreground/95">
									{example[sourceLanguage] || (
										<span className="italic opacity-70">
											<Translate text="words.translation_not_provided" />
										</span>
									)}
								</p>
							</div>
						))}
					</>
				) : (
					<p className="text-sm text-muted-foreground italic mb-2">
						<Translate text="words.no_examples_available" />
					</p>
				)}

				{/* Load More Button - Show when canLoadMore OR when loading examples */}
				{(canLoadMore || isLoadingExamples) && (
					<div className="flex justify-center mt-3">
						<Button
							variant="outline"
							size="sm"
							onClick={handleLoadMore}
							disabled={isLoadingExamples}
							className="h-8 px-3 text-xs"
						>
							{isLoadingExamples ? (
								<>
									<Loader2 className="h-3 w-3 animate-spin mr-1" />
									<Translate text="words.loading_examples" />
								</>
							) : (
								<>
									<ChevronDown className="h-3 w-3 mr-1" />
									<Translate text="words.load_more_examples" />
								</>
							)}
						</Button>
					</div>
				)}

				{/* Loading Skeleton for new examples */}
				{isLoadingExamples && (
					<div className="space-y-2 mt-2">
						{[1, 2, 3].map((i) => (
							<div key={`skeleton-${i}`} className="animate-pulse">
								<div className="pl-3 border-l-2 border-secondary/30 py-1">
									<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
									<div className="h-4 bg-muted-foreground/20 rounded mb-1 w-full"></div>
									<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
									<div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
								</div>
							</div>
						))}
					</div>
				)}

				{/* Error Message */}
				{exampleState.error && (
					<div className="text-xs text-destructive text-center mt-2">
						{exampleState.error}
					</div>
				)}

				{/* Total Count Info */}
				{(exampleState.total > 0 || (allExamples.length > 0 && !isFirstLoad)) && (
					<div className="text-xs text-muted-foreground text-center mt-2">
						<Translate text="words.showing_examples" /> {allExamples.length} /{' '}
						{exampleState.total > 0 ? exampleState.total : '20'}
						{isLoadingExamples && (
							<span className="ml-1 text-muted-foreground/50">
								(<Translate text="words.loading" />...)
							</span>
						)}
					</div>
				)}
			</div>
		</div>
	);
}

export const ExamplesList = memo(ExamplesListComponent);
