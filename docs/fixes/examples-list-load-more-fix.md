# Fix: Load More Examples Button hiện khi definition không có examples

## Vấn đề
<PERSON>t "Load More Examples" không hiển thị khi definition không có examples ban đầu, do logic render được đặt bên trong block `{allExamples.length > 0 ? ... : ...}`.

## Giải pháp
1. **Restructure UI Layout**: <PERSON> chuyển nút "Load More" ra ngoài conditional rendering của examples list
2. **Cải thiện canLoadMore logic**: Thêm điều kiện `totalExamplesShown < 20` để đảm bảo nút hiển thị khi chưa đạt limit

## Thay đổi

### File: `/src/components/examples-list.tsx`

#### Before:
```typescript
{allExamples.length > 0 ? (
  <div className="space-y-2">
    {/* examples rendering */}
    {canLoadMore && <LoadMoreButton />}
    {/* error and count info */}
  </div>
) : (
  <p>No examples available</p>
)}
```

#### After:
```typescript
<div className="space-y-2">
  {allExamples.length > 0 ? (
    <>{/* examples rendering */}</>
  ) : (
    <p>No examples available</p>
  )}
  
  {/* Load More Button - Luôn hiển thị khi canLoadMore = true */}
  {canLoadMore && <LoadMoreButton />}
  {/* error and count info */}
</div>
```

#### Logic cải thiện:
```typescript
const canLoadMore =
  wordId && 
  (hasMoreExamples || isFirstLoad || totalExamplesShown < 20) &&
  !exampleState.loading &&
  !generatingExamples;
```

## Test Cases
1. ✅ Definition với 0 examples → Hiển thị "No examples available" + nút "Load More"  
2. ✅ Definition với < 20 examples → Hiển thị examples + nút "Load More"
3. ✅ Definition với đúng 20 examples → Ẩn nút "Load More"
4. ✅ Loading state → Disable nút + hiển thị spinner

## Validation
- TypeScript compile: ✅ Passed
- Logic flow: ✅ Tested
- UI consistency: ✅ Maintained