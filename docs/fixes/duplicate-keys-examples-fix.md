# Fix: Duplicate Keys Error trong Examples List

## Vấn đề
Error: `Encountered two children with the same key, 'ed34e62e-b2da-4164-ba34-b349772688cb'. Keys should be unique...`

## Root Cause Analysis

### Nguyên nhân chính:
1. **Frontend Logic Issue**: 
   ```typescript
   // Trong ExamplesList component
   const allExamples = [...definition.examples, ...exampleState.examples];
   ```
   
2. **Backend Query Overlap**:
   - `definition.examples` chứa 3 examples đầu tiên từ `wordInclude` (take: 3)
   - `loadMoreExamples` với offset=0 cũng return 3 examples đầu tiên
   - → Duplicate IDs trong allExamples array

3. **React Key Collision**:
   ```typescript
   key={example.id || exIndex} // Same IDs → Same keys → React error
   ```

## G<PERSON><PERSON>i pháp thực hiện

### 1. **Frontend Deduplication Logic**
```typescript
// Before: Simple concatenation
const allExamples = [...definition.examples, ...exampleState.examples];

// After: Smart deduplication based on ID
const allExamples = React.useMemo(() => {
  const seenIds = new Set<string>();
  const combined = [];
  
  // Add initial examples first
  for (const example of definition.examples) {
    if (example.id && !seenIds.has(example.id)) {
      seenIds.add(example.id);
      combined.push(example);
    } else if (!example.id) {
      combined.push(example); // Safety for examples without ID
    }
  }
  
  // Add loaded examples, skipping duplicates
  for (const example of exampleState.examples) {
    if (example.id && !seenIds.has(example.id)) {
      seenIds.add(example.id);
      combined.push(example);
    } else if (!example.id) {
      combined.push(example); // Safety for examples without ID
    }
  }
  
  return combined;
}, [definition.examples, exampleState.examples]);
```

### 2. **Enhanced Key Generation**
```typescript
// Before: Simple fallback
key={example.id || exIndex}

// After: Unique fallback with content-based key
key={example.id || `example-${exIndex}-${example.EN?.slice(0, 20)}-${example.VI?.slice(0, 20)}`}
```

### 3. **Performance Optimization**
- Sử dụng `React.useMemo()` để tránh re-computation không cần thiết
- O(n) deduplication với Set cho efficiency
- Content-based fallback keys để tránh collision

## Technical Details

### Data Flow Issue:
```
Initial Load:
definition.examples = [example1, example2, example3] // from wordInclude (take: 3)

Load More (offset=0):
exampleState.examples = [example1, example2, example3] // Same examples!

Combined:
allExamples = [example1, example2, example3, example1, example2, example3] // DUPLICATES!
```

### After Fix:
```
Initial Load:
definition.examples = [example1, example2, example3]

Load More (offset=0):
exampleState.examples = [example1, example2, example3] // Same examples returned

Deduplication:
allExamples = [example1, example2, example3] // Only unique examples!
```

## Files Modified
- ✅ `/src/components/examples-list.tsx` - Added deduplication logic và enhanced keys
- ✅ Import React for useMemo

## Benefits
1. **🐛 Bug Fix**: Eliminates React duplicate key warnings/errors
2. **⚡ Performance**: useMemo prevents unnecessary recalculations  
3. **🔒 Reliability**: Handles edge cases (missing IDs, content-based fallbacks)
4. **📊 Data Integrity**: Ensures examples list accuracy without duplicates
5. **🎯 UX**: Smooth loading experience without visual glitches

## Test Scenarios Covered
- ✅ Load more khi có initial examples
- ✅ Load more khi không có initial examples  
- ✅ Multiple consecutive load more operations
- ✅ Examples không có ID (content-based keys)
- ✅ Mixed scenarios với initial + loaded examples

## Edge Cases Handled
- Examples without ID (fallback to content-based keys)
- Empty examples arrays
- Partial overlaps between initial và loaded examples
- React strict mode double rendering