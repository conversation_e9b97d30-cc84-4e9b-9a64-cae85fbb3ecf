# Enhancement: Loading UI cho "Load More Examples" Feature

## Vấn đề ban đầu
Loading UI cho "Load More Examples" feature chưa được handle tốt:
- Không có loading indicator trong title khi đang fetch examples
- <PERSON>hông có skeleton loading cho examples đang được tải
- Conflict giữa `generatingExamples` prop và `exampleState.loading`
- Button chỉ hiển thị khi `canLoadMore` mà không show trong lúc loading

## Cải thiện thực hiện

### 1. **Unified Loading State**
```typescript
// Before: Conflict giữa hai loading states
!exampleState.loading && !generatingExamples

// After: Unified loading state
const isLoadingExamples = exampleState.loading || generatingExamples;
const canLoadMore = wordId && (hasMoreExamples || isFirstLoad || totalExamplesShown < 20) && !isLoadingExamples;
```

### 2. **Enhanced Button Visibility**
```typescript
// Before: Chỉ hiển thị khi canLoadMore
{canLoadMore && <LoadMoreButton />}

// After: Hi<PERSON><PERSON> thị cả lúc loading để user biết đang process
{(canLoadMore || isLoadingExamples) && <LoadMoreButton />}
```

### 3. **Loading Indicator trong Title**
```typescript
<p className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
  <Translate text="words.examples" />:
  {isLoadingExamples && (
    <span className="flex items-center gap-1 text-xs text-muted-foreground/70">
      <Loader2 className="h-3 w-3 animate-spin" />
      <Translate text="words.loading" />
    </span>
  )}
</p>
```

### 4. **Skeleton Loading cho Examples**
```typescript
{isLoadingExamples && (
  <div className="space-y-2 mt-2">
    {[1, 2, 3].map((i) => (
      <div key={`skeleton-${i}`} className="animate-pulse">
        <div className="pl-3 border-l-2 border-secondary/30 py-1">
          <div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
          <div className="h-4 bg-muted-foreground/20 rounded mb-1 w-full"></div>
          <div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
          <div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
        </div>
      </div>
    ))}
  </div>
)}
```

### 5. **Enhanced Count Info**
```typescript
{/* Hiển thị loading status trong count */}
<div className="text-xs text-muted-foreground text-center mt-2">
  <Translate text="words.showing_examples" /> {allExamples.length} / {exampleState.total > 0 ? exampleState.total : '20'}
  {isLoadingExamples && (
    <span className="ml-1 text-muted-foreground/50">
      (<Translate text="words.loading" />...)
    </span>
  )}
</div>
```

## Translation Keys thêm vào
```typescript
// src/contexts/translations/words.trans.ts
'words.loading': {
  EN: 'Loading',
  VI: 'Đang tải',
},
```

## Files thay đổi
- ✅ `/src/components/examples-list.tsx` - Enhanced loading UI
- ✅ `/src/contexts/translations/words.trans.ts` - Added translation key

## User Experience Improvements

### Loading States theo thời gian:
1. **Initial State**: "No examples available" + "Load More" button
2. **Click Load More**: Button shows spinner + "Loading..." 
3. **Loading Process**: 
   - Title shows loading indicator
   - Skeleton examples appear
   - Button remains disabled với spinner
4. **Complete**: New examples appear + button returns to normal state

### Visual Feedback:
- ✅ **Immediate**: Button changes để show loading state
- ✅ **Progressive**: Skeleton cho thấy examples đang được load
- ✅ **Contextual**: Title indicator cho biết activity
- ✅ **Informative**: Count hiển thị loading status

## Validation
- TypeScript compile: ✅ Passed  
- Loading flow: ✅ Smooth transitions
- User feedback: ✅ Clear loading states
- Performance: ✅ No unnecessary re-renders